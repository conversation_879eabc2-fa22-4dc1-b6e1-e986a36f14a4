<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K<PERSON><PERSON> <PERSON><PERSON>ớp 10 THPT - Nam Định 2025-2026</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .filter-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            border: none;
        }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #667eea;
        }
        
        .cutoff-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: .8rem;
            margin-bottom: .8rem;
            box-shadow: 0 4px 6px rgba(40, 167, 69, 0.3);
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .table th {
            background-color: #667eea;
            color: white;
            font-weight: 600;
            border: none;
            text-align: center;
            vertical-align: middle;
            position: sticky;
            top: 0;
            z-index: 10;
            font-size: 0.9rem;
            padding: 0.75rem 0.5rem;
        }
        
        .table td {
            text-align: center;
            vertical-align: middle;
            border-color: #e9ecef;
            font-size: 0.9rem;
            padding: 0.75rem 0.5rem;
        }
        
        .rank-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 50px;
            font-weight: bold;
            font-size: 0.8rem;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: inline-block;
            min-width: 35px;
            text-align: center;
        }

        /* Color-coded rank badges based on performance */
        .rank-badge-high {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }

        .rank-badge-average {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: #333;
        }

        .rank-badge-low {
            background: linear-gradient(45deg, #dc3545, #e74c3c);
            color: white;
        }

        .rank-badge-default {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
        }

        /* Subject statistics styling */
        .subject-stats-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            height: 100%;
        }

        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .stats-item:last-child {
            margin-bottom: 0;
        }
        
        .status-passed {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 0.2rem 0.4rem;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.75rem;
        }
        
        .status-failed {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
            padding: 0.2rem 0.4rem;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.75rem;
        }
        
        .score-high {
            background-color: #d4edda;
            color: #155724;
            font-weight: bold;
        }
        
        .score-medium {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .score-low {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .passed-row {
            background-color: #d4edda !important;
        }
        
        .failed-row {
            background-color: #f8d7da !important;
        }
        
        .btn-register {
            background: linear-gradient(45deg, #fd7e14, #e55a4e);
            border: none;
            color: white;
            border-radius: 20px;
            padding: 0.3rem 0.8rem;
            font-size: 0.75rem;
            font-weight: bold;
            transition: transform 0.2s ease;
        }
        
        .btn-register:hover {
            transform: scale(1.05);
            color: white;
        }
        
        .cutoff-input {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 10px;
            padding: 0.75rem;
            width: 120px;
            text-align: center;
            font-weight: bold;
        }
        
        .cutoff-input:focus {
            background: white;
            color: #333;
            border-color: #fff;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        
        .cutoff-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        /* Compact bonus column styling */
        .bonus-column {
            max-width: 100px;
            font-size: 0.8rem;
        }
        
        .bonus-item {
            display: block;
            margin: 2px 0;
        }
        
        .bonus-ut {
            background: #e3f2fd;
            color: #1976d2;
            padding: 1px 4px;
            border-radius: 3px;
            font-size: 0.7rem;
        }
        
        .bonus-kk {
            background: #f3e5f5;
            color: #7b1fa2;
            padding: 1px 4px;
            border-radius: 3px;
            font-size: 0.7rem;
        }
        
        .bonus-chuyen {
            background: #fff3e0;
            color: #f57c00;
            padding: 1px 4px;
            border-radius: 3px;
            font-size: 0.7rem;
        }
        
        /* Visitor counter styling */
        .visitor-counter {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-top: 2rem;
            text-align: center;
            box-shadow: 0 4px 6px rgba(102, 126, 234, 0.3);
        }
        
        .counter-display {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 1rem 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .counter-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .table-responsive {
                font-size: 0.8rem;
            }
            
            .filter-card .row > div {
                margin-bottom: 1rem;
            }
            
            .bonus-column {
                max-width: 80px;
                font-size: 0.7rem;
            }
            
            .counter-display {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header-section">
        <div class="container">
            <div class="text-center">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="bi bi-mortarboard-fill me-3"></i>
                    Kết Quả Thi Tuyển Sinh Lớp 10 THPT Xuân Trường C
                </h1>
                <p class="lead">Năm học 2025-2026 - Sở Giáo dục và Đào tạo Nam Định</p>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Điểm chuẩn -->
        <div class="cutoff-card">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h3 class="mb-3">
                        <i class="bi bi-award-fill me-2"></i>Điểm Chuẩn Đợt 1
                    </h3>
                    <p class="mb-0">Điểm chuẩn dự kiến để đỗ vào lớp 10 THPT Xuân Trường C đợt 1</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end justify-content-center">
                        <label class="me-3 fw-bold">Điểm chuẩn:</label>
                        <input type="number" id="cutoffScore" class="cutoff-input" 
                               value="15.65" step="0.05" min="0" max="30"
                               onchange="updateCutoffScore()" placeholder="15.65" /disabled>
                        <!--<button class="btn btn-light ms-3" onclick="updateCutoffScore()">-->
                        <!--    <i class="bi bi-arrow-clockwise"></i> Cập nhật-->
                        <!--</button>-->
                    </div>
                </div>
            </div>
        </div>

        <!-- Thống kê tổng quan -->
        <div class="row mb-4" id="statsContainer">
            <div class="col-md-3">
                <div class="stats-card">
                    <h5 class="text-primary mb-2">
                        <i class="bi bi-people-fill me-2"></i>Tổng thí sinh
                    </h5>
                    <h3 class="mb-0" id="totalStudents">0</h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h5 class="text-success mb-2">
                        <i class="bi bi-check-circle-fill me-2"></i>Đỗ đợt 1
                    </h5>
                    <h3 class="mb-0 text-success" id="passedCount">0</h3>
                </div>
            </div>
           
            <div class="col-md-3">
                <div class="stats-card">
                    <h5 class="text-warning mb-2">
                        <i class="bi bi-trophy-fill me-2"></i>Điểm cao nhất
                    </h5>
                    <h3 class="mb-0" id="maxScore">0</h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h5 class="text-info mb-2">
                        <i class="bi bi-graph-up me-2"></i>Điểm TB
                    </h5>
                    <h3 class="mb-0" id="avgScore">0</h3>
                </div>
            </div>

        </div>

        <!-- Subject Statistics -->
        <div class="row mb-4" id="subjectStatsContainer">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-bar-chart-fill me-2"></i>Thống kê theo môn học
                            <button class="btn btn-sm btn-outline-secondary float-end" type="button" data-bs-toggle="collapse" data-bs-target="#subjectStats" aria-expanded="false">
                                <i class="bi bi-chevron-down"></i>
                            </button>
                        </h5>
                    </div>
                    <div class="collapse" id="subjectStats">
                        <div class="card-body">
                            <div class="row">
                                <!-- Ngữ văn stats -->
                                <div class="col-md-3 mb-3">
                                    <div class="subject-stats-card">
                                        <h6 class="text-primary mb-2">
                                            <i class="bi bi-book me-1"></i>Ngữ văn
                                        </h6>
                                        <div class="stats-item">
                                            <small class="text-muted">Điểm cao nhất:</small>
                                            <strong id="maxNguVan">0</strong>
                                        </div>
                                        <div class="stats-item">
                                            <small class="text-muted">Số điểm 10:</small>
                                            <strong id="perfectNguVan">0</strong>
                                        </div>
                                        <div class="stats-item">
                                            <small class="text-muted">Điểm TB:</small>
                                            <strong id="avgNguVan">0</strong>
                                        </div>
                                    </div>
                                </div>

                                <!-- Toán stats -->
                                <div class="col-md-3 mb-3">
                                    <div class="subject-stats-card">
                                        <h6 class="text-success mb-2">
                                            <i class="bi bi-calculator me-1"></i>Toán
                                        </h6>
                                        <div class="stats-item">
                                            <small class="text-muted">Điểm cao nhất:</small>
                                            <strong id="maxToan">0</strong>
                                        </div>
                                        <div class="stats-item">
                                            <small class="text-muted">Số điểm 10:</small>
                                            <strong id="perfectToan">0</strong>
                                        </div>
                                        <div class="stats-item">
                                            <small class="text-muted">Điểm TB:</small>
                                            <strong id="avgToan">0</strong>
                                        </div>
                                    </div>
                                </div>

                                <!-- Ngoại ngữ stats -->
                                <div class="col-md-3 mb-3">
                                    <div class="subject-stats-card">
                                        <h6 class="text-warning mb-2">
                                            <i class="bi bi-translate me-1"></i>Ngoại ngữ
                                        </h6>
                                        <div class="stats-item">
                                            <small class="text-muted">Điểm cao nhất:</small>
                                            <strong id="maxNgoaiNgu">0</strong>
                                        </div>
                                        <div class="stats-item">
                                            <small class="text-muted">Số điểm 10:</small>
                                            <strong id="perfectNgoaiNgu">0</strong>
                                        </div>
                                        <div class="stats-item">
                                            <small class="text-muted">Điểm TB:</small>
                                            <strong id="avgNgoaiNgu">0</strong>
                                        </div>
                                    </div>
                                </div>

                                <!-- Chuyên stats -->
                                <div class="col-md-3 mb-3">
                                    <div class="subject-stats-card">
                                        <h6 class="text-info mb-2">
                                            <i class="bi bi-star me-1"></i>Chuyên
                                        </h6>
                                        <div class="stats-item">
                                            <small class="text-muted">Điểm cao nhất:</small>
                                            <strong id="maxChuyen">0</strong>
                                        </div>
                                        <div class="stats-item">
                                            <small class="text-muted">Số điểm 10:</small>
                                            <strong id="perfectChuyen">0</strong>
                                        </div>
                                        <div class="stats-item">
                                            <small class="text-muted">Điểm TB:</small>
                                            <strong id="avgChuyen">0</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bộ lọc -->
        <div class="card filter-card">
            <div class="card-body">
                <h5 class="card-title mb-4">
                    <i class="bi bi-funnel-fill me-2"></i>Tìm kiếm và Lọc
                </h5>
                
                <div class="row">
                    <!-- Tìm kiếm SBD -->
                    <div class="col-md-3 mb-3">
                        <label class="form-label fw-semibold">
                            <i class="bi bi-search me-1"></i>Tìm theo SBD
                        </label>
                        <input type="text" class="form-control" id="searchSBD" 
                               placeholder="Nhập SBD..." onkeyup="filterData()">
                    </div>

                    <!-- Lọc theo trạng thái -->
                    <div class="col-md-3 mb-3">
                        <label class="form-label fw-semibold">
                            <i class="bi bi-funnel me-1"></i>Trạng thái đợt 1
                        </label>
                        <select class="form-select" id="statusFilter" onchange="filterData()">
                            <option value="">Tất cả</option>
                            <option value="passed">Đã đỗ đợt 1</option>
                            <option value="failed">Rớt đợt 1</option>
                        </select>
                    </div>

                    <!-- Lọc theo tổng điểm -->
                    <div class="col-md-3 mb-3">
                        <label class="form-label fw-semibold">
                            <i class="bi bi-calculator me-1"></i>Tổng không chuyên
                        </label>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" class="form-control" id="minTotal"
                                       placeholder="Từ" step="0.25" min="0" max="30" onchange="filterData()">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control" id="maxTotal"
                                       placeholder="Đến" step="0.25" min="0" max="30" onchange="filterData()">
                            </div>
                        </div>
                        <div class="mt-2">
                            <select class="form-select form-select-sm" id="totalScoreRange" onchange="applyTotalScoreRange()">
                                <option value="">Chọn khoảng điểm</option>
                                <option value="excellent">Xuất sắc (≥25)</option>
                                <option value="good">Giỏi (20-24.99)</option>
                                <option value="average">Khá (15-19.99)</option>
                                <option value="below_average">Trung bình (10-14.99)</option>
                                <option value="poor">Yếu (<10)</option>
                                <option value="passed">Đạt điểm chuẩn (≥15.65)</option>
                            </select>
                        </div>
                    </div>

                    <!-- Sắp xếp -->
                    <div class="col-md-3 mb-3">
                        <label class="form-label fw-semibold">
                            <i class="bi bi-sort-down me-1"></i>Sắp xếp theo
                        </label>
                        <select class="form-select" id="sortBy" onchange="sortData()">
                            <option value="rank">Thứ hạng</option>
                            <option value="sbd">Số báo danh</option>
                            <option value="tong_khong_chuyen">Tổng không chuyên</option>
                            <option value="status">Trạng thái đỗ/rớt</option>
                        </select>
                    </div>
                </div>

                <!-- Advanced Subject Filters -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-funnel-fill me-2"></i>Lọc theo điểm từng môn
                                    <button class="btn btn-sm btn-outline-secondary float-end" type="button" data-bs-toggle="collapse" data-bs-target="#subjectFilters" aria-expanded="false">
                                        <i class="bi bi-chevron-down"></i>
                                    </button>
                                </h6>
                            </div>
                            <div class="collapse" id="subjectFilters">
                                <div class="card-body">
                                    <div class="row">
                                        <!-- Ngữ văn filter -->
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label fw-semibold">Ngữ văn</label>
                                            <div class="row">
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm" id="minNguVan"
                                                           placeholder="Từ" step="0.25" min="0" max="10" onchange="filterData()">
                                                </div>
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm" id="maxNguVan"
                                                           placeholder="Đến" step="0.25" min="0" max="10" onchange="filterData()">
                                                </div>
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm" id="exactNguVan"
                                                           placeholder="Chính xác" step="0.25" min="0" max="10" onchange="filterData()">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Toán filter -->
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label fw-semibold">Toán</label>
                                            <div class="row">
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm" id="minToan"
                                                           placeholder="Từ" step="0.25" min="0" max="10" onchange="filterData()">
                                                </div>
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm" id="maxToan"
                                                           placeholder="Đến" step="0.25" min="0" max="10" onchange="filterData()">
                                                </div>
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm" id="exactToan"
                                                           placeholder="Chính xác" step="0.25" min="0" max="10" onchange="filterData()">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Ngoại ngữ filter -->
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label fw-semibold">Ngoại ngữ</label>
                                            <div class="row">
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm" id="minNgoaiNgu"
                                                           placeholder="Từ" step="0.25" min="0" max="10" onchange="filterData()">
                                                </div>
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm" id="maxNgoaiNgu"
                                                           placeholder="Đến" step="0.25" min="0" max="10" onchange="filterData()">
                                                </div>
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm" id="exactNgoaiNgu"
                                                           placeholder="Chính xác" step="0.25" min="0" max="10" onchange="filterData()">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Chuyên filter -->
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label fw-semibold">Chuyên</label>
                                            <div class="row">
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm" id="minChuyen"
                                                           placeholder="Từ" step="0.25" min="0" max="10" onchange="filterData()">
                                                </div>
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm" id="maxChuyen"
                                                           placeholder="Đến" step="0.25" min="0" max="10" onchange="filterData()">
                                                </div>
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm" id="exactChuyen"
                                                           placeholder="Chính xác" step="0.25" min="0" max="10" onchange="filterData()">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-12">
                                            <button class="btn btn-outline-secondary btn-sm" onclick="resetSubjectFilters()">
                                                <i class="bi bi-arrow-clockwise me-1"></i>Xóa bộ lọc môn học
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-3">
                        <select class="form-select" id="sortOrder" onchange="sortData()">
                            <option value="asc">Tăng dần</option>
                            <option value="desc">Giảm dần</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="itemsPerPage" onchange="changePageSize()">
                            <option value="25">25 dòng</option>
                            <option value="50">50 dòng</option>
                            <option value="100">100 dòng</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary" onclick="resetFilters()">
                            <i class="bi bi-arrow-clockwise me-1"></i>Đặt lại
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-success" onclick="exportResults()">
                            <i class="bi bi-download me-1"></i>Xuất Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bảng kết quả -->
        <div class="table-container">
            <div class="table-responsive" style="max-height: 800px; overflow-y: auto;">
                <table class="table table-hover mb-0" id="resultsTable">
                    <thead>
                        <tr>
                            <th style="width: 60px;">Hạng</th>
                            <th style="width: 80px;">SBD</th>
                            <th style="width: 100px;">Ưu tiên & KK</th>
                            <th style="width: 80px;">Ngữ văn</th>
                            <th style="width: 80px;">Toán</th>
                            <th style="width: 80px;">Ngoại ngữ</th>
                            <th style="width: 80px;">Chuyên</th>
                            <th style="width: 100px;">Tổng KC</th>
                            <th style="width: 100px;">Trạng thái</th>
                           
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- Dữ liệu sẽ được load bằng JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <nav aria-label="Phân trang" id="paginationContainer">
            <ul class="pagination justify-content-center mt-4" id="pagination">
                <!-- Pagination sẽ được tạo bằng JavaScript -->
            </ul>
        </nav>

        <!-- Visitor Counter -->
        <div class="visitor-counter" style="display:none">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <i class="bi bi-eye-fill" style="font-size: 3rem; opacity: 0.8;"></i>
                </div>
                <div class="col-md-4">
                    <div class="counter-label">Lượt truy cập hôm nay</div>
                    <div class="counter-display" id="todayVisitors">0</div>
                </div>
                <div class="col-md-4">
                    <div class="counter-label">Tổng lượt truy cập</div>
                    <div class="counter-display" id="totalVisitors">0</div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-center">
                    <small style="opacity: 0.8;">
                        <i class="bi bi-clock me-1"></i>
                        Cập nhật lần cuối: <span id="lastAccessTime"></span>
                    </small>
                </div>
            </div>
        </div>

        <!-- Footer info -->
        <div class="row mt-4 mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <p class="mb-2">
                            <strong>Hiển thị:</strong> <span id="showingInfo">0</span> | 
                            <strong>Tổng:</strong> <span id="totalFiltered">0</span> thí sinh
                        </p>
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            Dữ liệu từ Sở GD&ĐT Nam Định - Cập nhật: <span id="lastUpdate"></span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Biến toàn cục
        let allData = [];
        let filteredData = [];
        let currentPage = 1;
        let itemsPerPage = 25;
        let rankings = [];
        let cutoffScore = 15.65;
        let round2Registrations = new Set();

        // Visitor tracking
        let visitorData = {
            today: 0,
            total: 0,
            lastAccess: new Date()
        };

        // Load dữ liệu khi trang được tải
        document.addEventListener('DOMContentLoaded', function() {
            loadVisitorData();
            updateVisitorCount();
            loadData();
        });

        // Quản lý lượt truy cập
        function loadVisitorData() {
            const saved = localStorage.getItem('examVisitorData');
            if (saved) {
                visitorData = JSON.parse(saved);
                const today = new Date().toDateString();
                const lastAccessDate = new Date(visitorData.lastAccess).toDateString();
                
                // Reset counter hàng ngày
                if (today !== lastAccessDate) {
                    visitorData.today = 0;
                }
            }
        }

        function updateVisitorCount() {
            visitorData.today++;
            visitorData.total++;
            visitorData.lastAccess = new Date();
            
            localStorage.setItem('examVisitorData', JSON.stringify(visitorData));
            
            // Hiển thị với animation
            animateCounter('todayVisitors', visitorData.today);
            animateCounter('totalVisitors', visitorData.total);
            
            document.getElementById('lastAccessTime').textContent = 
                visitorData.lastAccess.toLocaleString('vi-VN');
        }

        function animateCounter(elementId, targetValue) {
            const element = document.getElementById(elementId);
            const startValue = parseInt(element.textContent) || 0;
            const increment = Math.ceil((targetValue - startValue) / 50);
            let currentValue = startValue;
            
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= targetValue) {
                    currentValue = targetValue;
                    clearInterval(timer);
                }
                element.textContent = currentValue.toLocaleString();
            }, 20);
        }

        // Hàm load dữ liệu từ file JSON
        async function loadData() {
            try {
                const response = await fetch('https://duogxaolin.com/bang-diem/data.json');
                const data = await response.json();
                
                allData = data.map(item => ({
                    ...item,
                    ngu_van_num: parseFloat(item.ngu_van) || 0,
                    toan_num: parseFloat(item.toan) || 0,
                    ngoai_ngu_num: parseFloat(item.ngoai_ngu) || 0,
                    tong_khong_chuyen_num: parseFloat(item.tong_khong_chuyen) || 0,
                    chuyen_num: parseFloat(item.chuyen) || 0,
                    tong_chuyen_num: parseFloat(item.tong_chuyen) || 0,
                    status: ''
                }));

                calculateRankings();
                updateCutoffScore();
                
                document.getElementById('lastUpdate').textContent = new Date().toLocaleDateString('vi-VN');
                
            } catch (error) {
                console.error('Lỗi khi tải dữ liệu:', error);
                alert('Không thể tải dữ liệu. Vui lòng kiểm tra file dữ liệu.');
            }
        }

        // Tính toán xếp hạng
        function calculateRankings() {
            const sorted = [...allData].sort((a, b) => {
                if (b.tong_khong_chuyen_num !== a.tong_khong_chuyen_num) {
                    return b.tong_khong_chuyen_num - a.tong_khong_chuyen_num;
                }
                if (b.toan_num !== a.toan_num) {
                    return b.toan_num - a.toan_num;
                }
                return b.ngu_van_num - a.ngu_van_num;
            });

            rankings = {};
            let currentRank = 1;
            
            for (let i = 0; i < sorted.length; i++) {
                const current = sorted[i];
                
                if (i > 0) {
                    const previous = sorted[i - 1];
                    if (current.tong_khong_chuyen_num !== previous.tong_khong_chuyen_num ||
                        current.toan_num !== previous.toan_num ||
                        current.ngu_van_num !== previous.ngu_van_num) {
                        currentRank = i + 1;
                    }
                }
                
                rankings[current.sbd] = currentRank;
            }
        }

        // Cập nhật điểm chuẩn
        function updateCutoffScore() {
            cutoffScore = parseFloat(document.getElementById('cutoffScore').value) || 15.65;
            
            allData.forEach(item => {
                if (item.tong_khong_chuyen_num >= cutoffScore) {
                    item.status = 'passed';
                } else {
                    item.status = 'failed';
                }
                
                
            });

            updateStatistics();
            filterData();
        }

        // Cập nhật thống kê
        function updateStatistics() {
            const validScores = allData.filter(item => item.tong_khong_chuyen_num > 0);
            const passedCount = allData.filter(item => item.status === 'passed').length;

            document.getElementById('totalStudents').textContent = allData.length.toLocaleString();
            document.getElementById('passedCount').textContent = passedCount.toLocaleString();

            if (validScores.length > 0) {
                const maxScore = Math.max(...validScores.map(item => item.tong_khong_chuyen_num));
                const avgScore = validScores.reduce((sum, item) => sum + item.tong_khong_chuyen_num, 0) / validScores.length;

                document.getElementById('maxScore').textContent = maxScore.toFixed(2);
                document.getElementById('avgScore').textContent = avgScore.toFixed(2);
            }

            updateSubjectStatistics();
        }

        // Update subject statistics
        function updateSubjectStatistics() {
            const dataToAnalyze = filteredData.length > 0 ? filteredData : allData;

            // Ngữ văn statistics
            const nguVanScores = dataToAnalyze.filter(item => item.ngu_van_num > 0).map(item => item.ngu_van_num);
            updateSubjectStats('NguVan', nguVanScores);

            // Toán statistics
            const toanScores = dataToAnalyze.filter(item => item.toan_num > 0).map(item => item.toan_num);
            updateSubjectStats('Toan', toanScores);

            // Ngoại ngữ statistics
            const ngoaiNguScores = dataToAnalyze.filter(item => item.ngoai_ngu_num > 0).map(item => item.ngoai_ngu_num);
            updateSubjectStats('NgoaiNgu', ngoaiNguScores);

            // Chuyên statistics
            const chuyenScores = dataToAnalyze.filter(item => item.chuyen_num > 0).map(item => item.chuyen_num);
            updateSubjectStats('Chuyen', chuyenScores);
        }

        // Helper function to update individual subject statistics
        function updateSubjectStats(subject, scores) {
            if (scores.length === 0) {
                document.getElementById(`max${subject}`).textContent = '0';
                document.getElementById(`perfect${subject}`).textContent = '0';
                document.getElementById(`avg${subject}`).textContent = '0';
                return;
            }

            const maxScore = Math.max(...scores);
            const perfectCount = scores.filter(score => score === 10).length;
            const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

            document.getElementById(`max${subject}`).textContent = maxScore.toFixed(2);
            document.getElementById(`perfect${subject}`).textContent = perfectCount.toLocaleString();
            document.getElementById(`avg${subject}`).textContent = avgScore.toFixed(2);
        }

        // Lọc dữ liệu
        function filterData() {
            const searchSBD = document.getElementById('searchSBD').value.trim().toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const minTotal = parseFloat(document.getElementById('minTotal').value) || 0;
            const maxTotal = parseFloat(document.getElementById('maxTotal').value) || 100;

            // Subject filters
            const minNguVan = parseFloat(document.getElementById('minNguVan').value);
            const maxNguVan = parseFloat(document.getElementById('maxNguVan').value);
            const exactNguVan = parseFloat(document.getElementById('exactNguVan').value);

            const minToan = parseFloat(document.getElementById('minToan').value);
            const maxToan = parseFloat(document.getElementById('maxToan').value);
            const exactToan = parseFloat(document.getElementById('exactToan').value);

            const minNgoaiNgu = parseFloat(document.getElementById('minNgoaiNgu').value);
            const maxNgoaiNgu = parseFloat(document.getElementById('maxNgoaiNgu').value);
            const exactNgoaiNgu = parseFloat(document.getElementById('exactNgoaiNgu').value);

            const minChuyen = parseFloat(document.getElementById('minChuyen').value);
            const maxChuyen = parseFloat(document.getElementById('maxChuyen').value);
            const exactChuyen = parseFloat(document.getElementById('exactChuyen').value);

            filteredData = allData.filter(item => {
                const matchSBD = !searchSBD || item.sbd.toLowerCase().includes(searchSBD);
                const matchStatus = !statusFilter ||
                    (statusFilter === 'passed' && item.status === 'passed') ||
                    (statusFilter === 'failed' && item.status === 'failed')
                const matchTotal = item.tong_khong_chuyen_num >= minTotal && item.tong_khong_chuyen_num <= maxTotal;

                // Subject matching logic
                const matchNguVan = checkSubjectMatch(item.ngu_van_num, minNguVan, maxNguVan, exactNguVan);
                const matchToan = checkSubjectMatch(item.toan_num, minToan, maxToan, exactToan);
                const matchNgoaiNgu = checkSubjectMatch(item.ngoai_ngu_num, minNgoaiNgu, maxNgoaiNgu, exactNgoaiNgu);
                const matchChuyen = checkSubjectMatch(item.chuyen_num, minChuyen, maxChuyen, exactChuyen);

                return matchSBD && matchStatus && matchTotal && matchNguVan && matchToan && matchNgoaiNgu && matchChuyen;
            });

            updateSubjectStatistics();
            sortData();
        }

        // Helper function to check subject score matching
        function checkSubjectMatch(score, min, max, exact) {
            if (!isNaN(exact)) {
                return score === exact;
            }

            const minCheck = isNaN(min) || score >= min;
            const maxCheck = isNaN(max) || score <= max;

            return minCheck && maxCheck;
        }

        // Sắp xếp dữ liệu
        function sortData() {
            const sortBy = document.getElementById('sortBy').value;
            const sortOrder = document.getElementById('sortOrder').value;

            filteredData.sort((a, b) => {
                let aVal, bVal;
                
                switch (sortBy) {
                    case 'rank':
                        aVal = rankings[a.sbd] || 999999;
                        bVal = rankings[b.sbd] || 999999;
                        break;
                    case 'sbd':
                        aVal = parseInt(a.sbd);
                        bVal = parseInt(b.sbd);
                        break;
                    case 'tong_khong_chuyen':
                        aVal = a.tong_khong_chuyen_num;
                        bVal = b.tong_khong_chuyen_num;
                        break;
                    case 'status':
                        aVal = a.status === 'passed' ? 1 : 0;
                        bVal = b.status === 'passed' ? 1 : 0;
                        break;
                    default:
                        aVal = rankings[a.sbd] || 999999;
                        bVal = rankings[b.sbd] || 999999;
                }

                if (sortOrder === 'desc') {
                    return bVal - aVal;
                } else {
                    return aVal - bVal;
                }
            });

            currentPage = 1;
            displayData();
        }

        // Hiển thị dữ liệu
        function displayData() {
            const tbody = document.getElementById('tableBody');
            
            if (filteredData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="10" class="text-center py-4">Không tìm thấy dữ liệu</td></tr>';
                return;
            }

            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageData = filteredData.slice(startIndex, endIndex);

            tbody.innerHTML = '';

            pageData.forEach((item, index) => {
                const row = document.createElement('tr');
                const rank = rankings[item.sbd] || '-';
                
                if (item.status === 'passed') {
                    row.classList.add('passed-row');
                } else if (item.status === 'failed') {
                    row.classList.add('failed-row');
                }
                
                const statusBadge = item.status === 'passed' ? 
                    '<span class="status-passed"><i class="bi bi-check-circle me-1"></i>Đỗ</span>' :
                    '<span class="status-failed"><i class="bi bi-x-circle me-1"></i>Rớt</span>';
                
                

                // Tạo cột bonus compact
                const bonusColumn = createBonusColumn(item);
                
                // Get color-coded rank badge class
                const rankBadgeClass = getRankBadgeClass(item.tong_khong_chuyen_num);

                row.innerHTML = `
                    <td><span class="rank-badge ${rankBadgeClass}">${rank}</span></td>
                    <td class="fw-bold">${item.sbd}</td>
                    <td class="bonus-column">${bonusColumn}</td>
                    <td class="${getScoreClass(item.ngu_van_num)}">${item.ngu_van || '-'}</td>
                    <td class="${getScoreClass(item.toan_num)}">${item.toan || '-'}</td>
                    <td class="${getScoreClass(item.ngoai_ngu_num)}">${item.ngoai_ngu || '-'}</td>
                    <td class="${getScoreClass(item.chuyen_num)}">${item.chuyen || '-'}</td>
                    <td class="fw-bold ${getScoreClass(item.tong_khong_chuyen_num, true)}">${item.tong_khong_chuyen || '-'}</td>
                    <td>${statusBadge}</td>

                `;
                
                tbody.appendChild(row);
            });

            updatePagination();
            updateShowingInfo();
        }

        // Tạo cột bonus compact
        function createBonusColumn(item) {
            let bonusItems = [];
            
            if (item.diem_ut && item.diem_ut.trim() !== '') {
                bonusItems.push(`<span class="bonus-item bonus-ut">ƯT: ${item.diem_ut}</span>`);
            }
            
            if (item.diem_kk && item.diem_kk.trim() !== '') {
                bonusItems.push(`<span class="bonus-item bonus-kk">KK: ${item.diem_kk}</span>`);
            }
            
            return bonusItems.length > 0 ? bonusItems.join('') : '-';
        }

        // Lấy class cho điểm số
        function getScoreClass(score, isTotal = false) {
            if (!score) return '';

            if (isTotal) {
                if (score >= cutoffScore) return 'score-high';
                if (score >= cutoffScore - 3) return 'score-medium';
                return 'score-low';
            } else {
                if (score >= 8) return 'score-high';
                if (score >= 6) return 'score-medium';
                return 'score-low';
            }
        }

        // Get color-coded rank badge class based on total score
        function getRankBadgeClass(totalScore) {
            if (!totalScore) return 'rank-badge-default';

            if (totalScore >= 20) return 'rank-badge-high';      // High performers (≥20)
            if (totalScore >= 15) return 'rank-badge-average';   // Average performers (15-19.99)
            return 'rank-badge-low';                             // Low performers (<15)
        }

        // Apply total score range filter
        function applyTotalScoreRange() {
            const range = document.getElementById('totalScoreRange').value;
            const minTotalInput = document.getElementById('minTotal');
            const maxTotalInput = document.getElementById('maxTotal');

            switch (range) {
                case 'excellent':
                    minTotalInput.value = '25';
                    maxTotalInput.value = '30';
                    break;
                case 'good':
                    minTotalInput.value = '20';
                    maxTotalInput.value = '24.99';
                    break;
                case 'average':
                    minTotalInput.value = '15';
                    maxTotalInput.value = '19.99';
                    break;
                case 'below_average':
                    minTotalInput.value = '10';
                    maxTotalInput.value = '14.99';
                    break;
                case 'poor':
                    minTotalInput.value = '0';
                    maxTotalInput.value = '9.99';
                    break;
                case 'passed':
                    minTotalInput.value = cutoffScore.toString();
                    maxTotalInput.value = '30';
                    break;
                default:
                    minTotalInput.value = '';
                    maxTotalInput.value = '';
            }

            filterData();
        }

        // Reset subject filters
        function resetSubjectFilters() {
            document.getElementById('minNguVan').value = '';
            document.getElementById('maxNguVan').value = '';
            document.getElementById('exactNguVan').value = '';

            document.getElementById('minToan').value = '';
            document.getElementById('maxToan').value = '';
            document.getElementById('exactToan').value = '';

            document.getElementById('minNgoaiNgu').value = '';
            document.getElementById('maxNgoaiNgu').value = '';
            document.getElementById('exactNgoaiNgu').value = '';

            document.getElementById('minChuyen').value = '';
            document.getElementById('maxChuyen').value = '';
            document.getElementById('exactChuyen').value = '';

            filterData();
        }


        // Cập nhật phân trang
        function updatePagination() {
            const pagination = document.getElementById('pagination');
            const totalPages = Math.ceil(filteredData.length / itemsPerPage);
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            pagination.innerHTML = '';

            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">‹</a>`;
            pagination.appendChild(prevLi);

            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
                pagination.appendChild(li);
            }

            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">›</a>`;
            pagination.appendChild(nextLi);
        }

        // Thay đổi trang
        function changePage(page) {
            const totalPages = Math.ceil(filteredData.length / itemsPerPage);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                displayData();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }

        // Thay đổi số item per page
        function changePageSize() {
            itemsPerPage = parseInt(document.getElementById('itemsPerPage').value);
            currentPage = 1;
            displayData();
        }

        // Cập nhật thông tin hiển thị
        function updateShowingInfo() {
            const startIndex = (currentPage - 1) * itemsPerPage + 1;
            const endIndex = Math.min(currentPage * itemsPerPage, filteredData.length);
            
            document.getElementById('showingInfo').textContent = 
                `${startIndex.toLocaleString()} - ${endIndex.toLocaleString()}`;
            document.getElementById('totalFiltered').textContent = filteredData.length.toLocaleString();
        }

        // Reset filters
        function resetFilters() {
            document.getElementById('searchSBD').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('minTotal').value = '';
            document.getElementById('maxTotal').value = '';
            document.getElementById('totalScoreRange').value = '';
            document.getElementById('sortBy').value = 'rank';
            document.getElementById('sortOrder').value = 'asc';
            document.getElementById('itemsPerPage').value = '25';

            // Reset subject filters
            resetSubjectFilters();

            currentPage = 1;
            filterData();
        }

        // Export kết quả
        function exportResults() {
            let csvContent = "STT,SBD,Điểm ưu tiên,Điểm khu vực,Ngữ văn,Toán,Ngoại ngữ,Chuyên,Tổng không chuyên,Tổng chuyên,Trạng thái\n";
            
            filteredData.forEach((item, index) => {
                const status = item.status === 'passed' ? 'Đỗ đợt 1' : 'Rớt đợt 1';
                const round2 = item.round2_registered ? 'Có' : 'Không';
                csvContent += `${index + 1},"${item.sbd}","${item.diem_ut || ''}","${item.diem_kk || ''}","${item.ngu_van || ''}","${item.toan || ''}","${item.ngoai_ngu || ''}","${item.chuyen || ''}","${item.tong_khong_chuyen || ''}","${item.tong_chuyen || ''}","${status}"\n`;
            });
            
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `ket_qua_thi_lop10_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
        }
    </script>
</body>
</html>
